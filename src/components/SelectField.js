import React, { useState, useEffect, useRef } from "react";
import {
  Image,
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  FlatList,
  Animated,
  Dimensions,
} from "react-native";
import PropTypes from "prop-types";

import MyText from "./MyText";
import colors from "../assets/colors";
import icons from "../assets/icons";
import commonStyles from "../assets/commonStyles";

const { height: SCREEN_HEIGHT } = Dimensions.get("window");

const CustomDropDown = ({
  width = "100%",
  data = [],
  onSelect,
  defaultValue = "Select",
  imageIcon,
  label,
  labelStyle,
  labelIcon,
  height = 0,
  backgroundColor = colors.inputBg,
  radius = 12,
  borderWidth = 0,
  borderColor,
  disabled = false,
  showIcon = true,
  onDropdownToggle,
  showVisibilityToggle = false,
  isVisible = false,
  onVisibilityToggle,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [dropdownPosition, setDropdownPosition] = useState("bottom");
  const [buttonLayout, setButtonLayout] = useState({ y: 0, height: 0 });
  const dropdownButtonRef = useRef(null);
  const rotateAnim = useRef(new Animated.Value(0)).current;

  // Find the initial selected item based on defaultValue
  useEffect(() => {
    if (!Array.isArray(data) || data.length === 0) return;

    if (typeof defaultValue === "string") {
      const matchedItem = data.find(
        (item) =>
          item && (item.name === defaultValue || item.label === defaultValue)
      );
      if (matchedItem) {
        setSelectedItem(matchedItem);
      }
    }
  }, [defaultValue, data]);

  // Handle dropdown animation
  useEffect(() => {
    Animated.timing(rotateAnim, {
      toValue: isOpen ? 1 : 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  }, [isOpen]);

  const toggleDropdown = () => {
    if (disabled) return;

    const newIsOpen = !isOpen;

    if (!isOpen) {
      // Measure the position of the dropdown button
      dropdownButtonRef.current.measure((x, y, width, height, pageX, pageY) => {
        const spaceBelow = SCREEN_HEIGHT - pageY - height;
        const requiredSpace = Math.min(data.length * 40, 200); // Estimate dropdown height

        // If there's not enough space below, position the dropdown above
        if (spaceBelow < requiredSpace) {
          setDropdownPosition("top");
        } else {
          setDropdownPosition("bottom");
        }

        setButtonLayout({ y: pageY, height });
      });
    }

    // Notify parent component about dropdown state change
    if (onDropdownToggle) {
      onDropdownToggle(newIsOpen);
    }

    setIsOpen(newIsOpen);
  };

  const handleSelect = (item) => {
    setSelectedItem(item);
    setIsOpen(false);

    // Notify parent component that dropdown is closed
    if (onDropdownToggle) {
      onDropdownToggle(false);
    }

    if (onSelect) {
      onSelect(item);
    }
  };

  const getDisplayText = () => {
    if (selectedItem?.name || selectedItem?.label) {
      return selectedItem.name || selectedItem.label;
    }
    if (typeof defaultValue === "object" && defaultValue) {
      return defaultValue.name || defaultValue.label || "Select";
    }
    return "Select";
  };

  const rotate = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ["0deg", "90deg"],
  });

  return (
    <View style={{ marginBottom: 16 }}>
      {/* Label */}
      {label && (
        <View
          style={[
            {
              flexDirection: "row",
              alignItems: "center",
              position: "relative",
            },
          ]}
        >
          {labelIcon && <Image source={labelIcon} style={styles.labelIcon} />}
          <MyText p medium style={[styles.label, labelStyle]}>
            {label}
          </MyText>
        </View>
      )}

      {/* Main container with relative positioning */}
      <View style={{ position: "relative", zIndex: isOpen ? 999 : 1 }}>
        {/* Dropdown Button */}
        <View style={{ position: "relative" }}>
          <TouchableOpacity
            ref={dropdownButtonRef}
            style={[
              styles.dropdownButton,
              {
                width: width,
                height: height || 48,
                backgroundColor,
                borderRadius: radius,
                borderWidth,
                borderColor,
                opacity: disabled ? 0.7 : 1,
                borderBottomLeftRadius:
                  isOpen && dropdownPosition === "bottom" ? 0 : radius,
                borderBottomRightRadius:
                  isOpen && dropdownPosition === "bottom" ? 0 : radius,
                borderTopLeftRadius:
                  isOpen && dropdownPosition === "top" ? 0 : radius,
                borderTopRightRadius:
                  isOpen && dropdownPosition === "top" ? 0 : radius,
              },
              showVisibilityToggle && { paddingRight: 40 }, // Add padding for the eye icon
            ]}
            onPress={toggleDropdown}
            activeOpacity={0.7}
            disabled={disabled}
          >
            <MyText
              style={[
                styles.dropdownButtonText,
                {
                  textAlign: showIcon ? "left" : "center",
                  color: selectedItem ? colors.black : colors.txtGray,
                },
              ]}
            >
              {getDisplayText()}
            </MyText>
            {showIcon && (
              <Animated.Image
                source={icons.rightArrowIcon}
                style={[
                  commonStyles?.extraSmallIcon,
                  { transform: [{ rotate }] },
                ]}
                resizeMode={"contain"}
              />
            )}
          </TouchableOpacity>

          {/* Visibility Toggle */}
          {showVisibilityToggle && (
            <TouchableOpacity
              style={styles.visibilityToggle}
              onPress={onVisibilityToggle}
              activeOpacity={0.7}
              disabled={disabled}
            >
              <Image
                source={
                  isVisible
                    ? icons.openEyeProfileIcon
                    : icons.closedEyeProfileIcon
                }
                style={styles.visibilityIcon}
                resizeMode="contain"
              />
            </TouchableOpacity>
          )}
        </View>

        {/* Dropdown List - Positioned based on available space */}
        {isOpen && (
          <View
            style={[
              styles.dropdown,
              dropdownPosition === "top"
                ? styles.dropdownTop
                : styles.dropdownBottom,
              {
                width: width,
                maxHeight: 200,
                borderTopLeftRadius: dropdownPosition === "bottom" ? 0 : 8,
                borderTopRightRadius: dropdownPosition === "bottom" ? 0 : 8,
                borderBottomLeftRadius: dropdownPosition === "top" ? 0 : 8,
                borderBottomRightRadius: dropdownPosition === "top" ? 0 : 8,
              },
            ]}
          >
            <FlatList
              data={data}
              keyExtractor={(_, index) => `dropdown-item-${index}`}
              showsVerticalScrollIndicator={false}
              bounces={false}
              renderItem={({ item }) => {
                const isSelected =
                  (selectedItem?.value &&
                    item?.value &&
                    selectedItem.value === item.value) ||
                  (selectedItem?.label &&
                    item?.label &&
                    selectedItem.label === item.label);

                return (
                  <TouchableOpacity
                    style={[
                      styles.dropdownItem,
                      isSelected && { backgroundColor: colors.primary },
                    ]}
                    onPress={() => handleSelect(item)}
                  >
                    {imageIcon && (
                      <Image
                        source={imageIcon}
                        style={styles.dropdownItemIcon}
                      />
                    )}
                    <Text
                      style={[
                        styles.dropdownItemText,
                        { color: isSelected ? colors.white : colors.black },
                      ]}
                    >
                      {item.label || item.name || "Unknown"}
                    </Text>
                  </TouchableOpacity>
                );
              }}
            />
          </View>
        )}
      </View>
    </View>
  );
};

CustomDropDown.propTypes = {
  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  data: PropTypes.array,
  onSelect: PropTypes.func,
  defaultValue: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  imageIcon: PropTypes.any,
  label: PropTypes.string,
  labelStyle: PropTypes.object,
  labelIcon: PropTypes.any,
  height: PropTypes.number,
  backgroundColor: PropTypes.string,
  radius: PropTypes.number,
  borderWidth: PropTypes.number,
  borderColor: PropTypes.string,
  disabled: PropTypes.bool,
  showIcon: PropTypes.bool,
  marginVertical: PropTypes.number,
  onDropdownToggle: PropTypes.func,
  showVisibilityToggle: PropTypes.bool,
  isVisible: PropTypes.bool,
  onVisibilityToggle: PropTypes.func,
};

const styles = StyleSheet.create({
  dropdownButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 12,
    marginTop: 6,
  },
  dropdownButtonText: {
    flex: 1,
    fontSize: 14,
    fontFamily: "Metropolis-Regular",
  },
  dropdownIcon: {
    width: 20,
    height: 20,
    resizeMode: "contain",
  },
  labelIcon: {
    width: 16,
    height: 16,
    marginRight: 8,
    resizeMode: "contain",
  },
  dropdown: {
    position: "absolute",
    left: 0,
    right: 0,
    backgroundColor: colors.inputBg,
    borderRadius: 8,
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    zIndex: 1000,
  },
  dropdownTop: {
    bottom: "100%",
    marginBottom: 0,
  },
  dropdownBottom: {
    top: "100%",
    marginTop: 0,
  },
  dropdownItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 10,
    paddingHorizontal: 12,
  },
  dropdownItemText: {
    flex: 1,
    fontSize: 14,
    fontFamily: "Metropolis-Regular",
  },
  dropdownItemIcon: {
    width: 20,
    height: 20,
    marginRight: 8,
  },
  visibilityToggle: {
    position: "absolute",
    right: 12,
    top: "50%",
    transform: [{ translateY: -10 }],
    padding: 5,
    zIndex: 1,
  },
  visibilityIcon: {
    width: 20,
    height: 20,
  },
});

export default CustomDropDown;
