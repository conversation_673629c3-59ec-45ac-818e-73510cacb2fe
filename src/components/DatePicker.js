import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  Platform,
} from "react-native";
import PropTypes from "prop-types";
import colors from "../assets/colors";
import MyText from "./MyText";
import icons from "../assets/icons";
import DateTimePickerModal from "react-native-modal-datetime-picker";

const DatePicker = ({
  value,
  onChangeDate,
  label,
  labelStyle,
  error,
  placeholder = "Select date",
  minimumDate,
  maximumDate,
  disabled = false,
  containerStyle,
  mode = "date",
  format = "YYYY-MM-DD",
  showVisibilityToggle = false,
  isVisible = false,
  onVisibilityToggle,
}) => {
  const [isPickerVisible, setPickerVisible] = useState(false);

  const showDatePicker = () => {
    if (!disabled) {
      setPickerVisible(true);
    }
  };

  const hideDatePicker = () => {
    setPickerVisible(false);
  };

  const handleConfirm = (date) => {
    hideDatePicker();
    if (onChangeDate) {
      onChangeDate(date);
    }
  };

  // Format the date for display
  const formatDate = (date) => {
    if (!date) return placeholder;

    try {
      if (typeof date === "string") {
        date = new Date(date);
      }

      if (mode === "date") {
        return date.toLocaleDateString();
      } else if (mode === "time") {
        return date.toLocaleTimeString([], {
          hour: "2-digit",
          minute: "2-digit",
        });
      } else {
        return date.toLocaleString();
      }
    } catch (error) {
      console.error("Error formatting date:", error);
      return placeholder;
    }
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {label && (
        <MyText p medium style={[styles.label, labelStyle]}>
          {label}
        </MyText>
      )}

      <View style={{ position: "relative" }}>
        <TouchableOpacity
          activeOpacity={0.8}
          style={[
            styles.datePickerButton,
            containerStyle,
            error && styles.errorBorder,
            disabled && styles.disabled,
            showVisibilityToggle && { paddingRight: 40 }, // Add padding for the eye icon
          ]}
          onPress={showDatePicker}
          disabled={disabled}
        >
          <MyText
            style={{
              color: value ? colors.black : colors.txtGray,
            }}
            p
          >
            {value ? formatDate(value) : placeholder}
          </MyText>
          <Image
            source={icons.calendarIcon || icons.dropdownIcon}
            style={styles.icon}
            resizeMode="contain"
          />
        </TouchableOpacity>

        {/* Visibility Toggle */}
        {showVisibilityToggle && (
          <TouchableOpacity
            style={styles.visibilityToggle}
            onPress={onVisibilityToggle}
            activeOpacity={0.7}
            disabled={disabled}
          >
            <Image
              source={
                isVisible
                  ? icons.openEyeProfileIcon
                  : icons.closedEyeProfileIcon
              }
              style={styles.visibilityIcon}
              resizeMode="contain"
            />
          </TouchableOpacity>
        )}
      </View>

      {error && <Text style={styles.errorText}>{error}</Text>}

      <DateTimePickerModal
        isVisible={isPickerVisible}
        mode={mode}
        onConfirm={handleConfirm}
        onCancel={hideDatePicker}
        minimumDate={minimumDate}
        maximumDate={maximumDate}
        date={value ? new Date(value) : new Date()}
        is24Hour={true}
        display={Platform.OS === "ios" ? "spinner" : "default"}
      />
    </View>
  );
};

DatePicker.propTypes = {
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.instanceOf(Date)]),
  onChangeDate: PropTypes.func,
  label: PropTypes.string,
  error: PropTypes.string,
  placeholder: PropTypes.string,
  minimumDate: PropTypes.instanceOf(Date),
  maximumDate: PropTypes.instanceOf(Date),
  disabled: PropTypes.bool,
  containerStyle: PropTypes.object,
  mode: PropTypes.oneOf(["date", "time", "datetime"]),
  format: PropTypes.string,
  showVisibilityToggle: PropTypes.bool,
  isVisible: PropTypes.bool,
  onVisibilityToggle: PropTypes.func,
};

const styles = StyleSheet.create({
  container: {
    width: "100%",
    marginBottom: 20,
  },
  labelContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  label: {
    flex: 1,
  },
  datePickerButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#f2f2f2",
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 14,
    height: 48,
  },
  icon: {
    width: 20,
    height: 20,
    tintColor: colors.black,
  },
  errorBorder: {
    borderColor: colors.red,
    borderWidth: 1,
  },
  errorText: {
    color: colors.red,
    fontSize: 12,
    marginTop: 4,
  },
  disabled: {
    backgroundColor: colors.lightGray || "#e0e0e0",
    opacity: 0.7,
  },
  visibilityToggle: {
    position: "absolute",
    right: 12,
    top: "50%",
    transform: [{ translateY: -10 }],
    padding: 5,
    zIndex: 1,
  },
  visibilityIcon: {
    width: 20,
    height: 20,
  },
});

export default DatePicker;
