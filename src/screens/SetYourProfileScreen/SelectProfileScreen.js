import React, { useState, useEffect, useCallback } from "react";
import {
  Image,
  StyleSheet,
  View,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { useSelector, useDispatch } from "react-redux";
import Header from "../../components/Header";
import icons from "../../assets/icons";
import AppLoader from "../../components/AppLoader";
import MyText from "../../components/MyText";
import commonStyles from "../../assets/commonStyles";
import ChipSelector from "../../components/ChipSelector";
import colors from "../../assets/colors";
import InputField from "../../components/InputField";
import { PrimaryButton } from "../../components/Button";
import { setProfileContactsData } from "../../redux/features/mainSlice";
import { storeProfile } from "../../redux/features/SharingProfileSlice";
import { showToast } from "../../utils/toastConfig";

const PROFILE_OPTIONS = [
  {
    label: "Home",
    value: "home",
    icon: <Image source={icons.HomeIconProfile} resizeMode="contain" />,
  },
  {
    label: "Work",
    value: "work",
    icon: <Image source={icons.WorkIconProfile} resizeMode="contain" />,
  },
  {
    label: "Family",
    value: "family",
    icon: <Image source={icons.FamilyIconProfile} resizeMode="contain" />,
  },
  {
    label: "Friends",
    value: "friends",
    icon: <Image source={icons.FriendsIconProfile} resizeMode="contain" />,
  },
];

const getMatchingOption = (text) =>
  PROFILE_OPTIONS.find(
    (option) => option.label.toLowerCase() === text.toLowerCase()
  );

const SelectProfileScreen = ({ navigation, route }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [selectedTab, setSelectedTab] = useState("home");
  const [profileName, setProfileName] = useState("Home");
  const [selectedContacts, setSelectedContacts] = useState([]);
  const [errors, setErrors] = useState({ profileName: "", contacts: "" });

  const dispatch = useDispatch();
  const storeProfileState = useSelector(
    (state) => state.sharingProfileSlice.storeProfile
  );
  const { data, loading, error } = storeProfileState;
  const profileContactsData = useSelector(
    (state) => state.mainSlice.profileContactsData
  );

  // Sync selected contacts from redux
  useEffect(() => {
    setSelectedContacts(profileContactsData || []);
  }, [profileContactsData]);

  // Clear contacts on tab press
  useEffect(() => {
    const unsubscribe = navigation.addListener("tabPress", () => {
      dispatch(setProfileContactsData([]));
    });
    return unsubscribe;
  }, [navigation, dispatch]);

  // Update profile name when tab changes
  useEffect(() => {
    const selectedOption = PROFILE_OPTIONS.find(
      (option) => option.value === selectedTab
    );
    if (selectedOption) setProfileName(selectedOption.label);
  }, [selectedTab]);

  // Handlers
  const handleProfileNameChange = useCallback((text) => {
    setProfileName(text);
    const matchingOption = getMatchingOption(text);
    setSelectedTab(matchingOption ? matchingOption.value : "");
    setErrors((prev) => ({
      ...prev,
      profileName: text.trim() === "" ? "Profile name is required" : "",
    }));
  }, []);

  const handleCreateNewProfile = useCallback(() => {
    setProfileName("");
    setSelectedTab("");
  }, []);

  const navigateToContactSelection = useCallback(() => {
    navigation.navigate("AddContactsScreen", { fromProfile: true });
  }, [navigation]);

  const validateForm = () => {
    let isValid = true;
    const newErrors = { ...errors };
    if (profileName.trim() === "") {
      newErrors.profileName = "Profile name is required";
      isValid = false;
    }
    if (selectedContacts.length === 0) {
      newErrors.contacts = "Please select at least one contact";
      isValid = false;
    }
    setErrors(newErrors);
    return isValid;
  };

  const handleSaveProfile = async () => {
    if (!validateForm()) return;
    setIsLoading(true);
    const apiBody = {
      profile_name: profileName,
      addInProfile: selectedContacts.map((c) => ({ memberId: c.id })),
    };
    try {
      const response = await dispatch(storeProfile(apiBody)).unwrap();
      console.log(
        "🚀 ~ handleSaveProfile ~ response:",
        JSON.stringify(response, null, 2)
      );
      setIsLoading(false);
      const isSuccess =
        response.success || (response.payload && response.payload.success);
      if (isSuccess) {
        showToast(
          "success",
          response?.payload?.message ||
            response?.message ||
            "Contacts added successfully."
        );
        navigation.navigate("ProfileCompletionScreen", {
          profileId: response?.data?._id,
        });
      } else {
        console.log("i m in else ");
        showToast(
          "error",
          response?.payload?.message ||
            response?.message ||
            "Failed to add contacts."
        );
      }
    } catch (e) {
      showToast(
        "error",
        e?.payload?.message || e?.message || "Failed to add contacts."
      );
      setIsLoading(false);
    }
  };

  return (
    <View style={{ flex: 1, backgroundColor: colors.white }}>
      <Header
        title="Set Your Profile"
        leftIcon={icons?.backwBg}
        onPressLeft={() => navigation.goBack()}
        pb={20}
        textCenter
      />
      <AppLoader isLoading={isLoading} />
      <ScrollView style={{ flex: 1 }}>
        <View style={styles.contentContainer}>
          <MyText h6 semibold>
            Select Profile
          </MyText>
          <View style={commonStyles.row}>
            <ChipSelector
              options={PROFILE_OPTIONS}
              selectedValue={selectedTab}
              onSelect={setSelectedTab}
              containerStyle={styles.chipContainer}
            />
          </View>
          <View style={{ marginTop: 10 }}>
            <InputField
              label="Profile Name*"
              value={profileName}
              onChangeText={handleProfileNameChange}
              error={errors.profileName}
              placeholder="Enter profile name"
            />
            <MyText
              p
              medium
              underline
              style={{ alignSelf: "flex-end" }}
              onPress={handleCreateNewProfile}
            >
              +Create New Profile
            </MyText>
            <TouchableOpacity onPress={navigateToContactSelection}>
              <InputField
                label="Select Contacts"
                value={
                  selectedContacts.length > 0
                    ? `${selectedContacts.length} contacts selected`
                    : ""
                }
                editable={false}
                error={errors.contacts}
                placeholder="Tap to select contacts"
                rightIcon={icons.rightArrow}
                disabled
              />
            </TouchableOpacity>
            {/* Show selected contacts as chips */}
            {selectedContacts.length > 0 && (
              <View
                style={{
                  flexDirection: "row",
                  flexWrap: "wrap",
                  marginTop: 10,
                }}
              >
                {selectedContacts.map((contact) => (
                  <View
                    key={contact.id}
                    style={{
                      backgroundColor: colors.primary || "#e0e0e0",
                      borderRadius: 16,
                      paddingHorizontal: 12,
                      paddingVertical: 6,
                      marginRight: 8,
                      marginBottom: 8,
                    }}
                  >
                    <MyText style={{ color: "#fff", fontSize: 14 }}>
                      {contact.name}
                    </MyText>
                  </View>
                ))}
              </View>
            )}
          </View>
          <PrimaryButton
            title="Save Profile"
            style={{ marginTop: 30 }}
            onPress={handleSaveProfile}
          />
        </View>
      </ScrollView>
    </View>
  );
};

export default SelectProfileScreen;

const styles = StyleSheet.create({
  contentContainer: {
    flex: 1,
    padding: 20,
  },
  chipContainer: {
    width: "100%",
    gap: 10,
  },
});
