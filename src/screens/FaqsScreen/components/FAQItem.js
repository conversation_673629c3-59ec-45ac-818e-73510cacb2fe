import React from "react";
import { View, Text, TouchableOpacity, StyleSheet, Image } from "react-native";
import MyText from "../../../components/MyText";
import icons from "../../../assets/icons";
import colors from "../../../assets/colors";

const FAQItem = ({ item, isExpanded, onToggle }) => {
  return (
    <View style={styles.container}>
      <TouchableOpacity style={styles.row} onPress={onToggle}>
        <Image
          source={icons.FaqIcon}
          style={styles.icon}
          resizeMode="contain"
        />
        <MyText bold style={styles.title}>
          {item.question}
        </MyText>

        {/* <Image
          source={isExpanded ? icons.minusIcon : icons.plusIcon}
          style={styles.toggleIcon}
          resizeMode="contain"
        /> */}
      </TouchableOpacity>

      {isExpanded && (
        <MyText color="#888" style={styles.answer}>
          {item.answer}
        </MyText>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
    paddingHorizontal: 16,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
  },
  icon: {
    width: 20,
    height: 20,
    tintColor: colors.primary,
    marginRight: 10,
  },
  toggleIcon: {
    width: 18,
    height: 18,
    // tintColor: '#222',
    // marginLeft: 'auto',
  },
  title: {
    flex: 1,
    fontSize: 14,
  },
  answer: {
    marginTop: 8,
    fontSize: 13,
    lineHeight: 18,
  },
});

export default FAQItem;
