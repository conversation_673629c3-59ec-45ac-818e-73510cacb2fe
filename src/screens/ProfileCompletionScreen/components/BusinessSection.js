import React, { useState } from "react";
import { View, Text, TouchableOpacity, Alert, Image } from "react-native";
import PropTypes from "prop-types";
import InputField from "../../../components/InputField";
import PhoneNumberField from "../../../components/PhoneNumberField";
import CollapsibleHeader from "./CollapsibleHeader";
import SelectField from "../../../components/SelectField";
import { styles } from "../styles";
import { industryOptions } from "../../../utils/industryOptions";
import ImagePicker from "react-native-image-crop-picker";
import colors from "../../../assets/colors";
import icons from "../../../assets/icons";
import commonStyles from "../../../assets/commonStyles";

const BusinessSection = React.forwardRef(
  ({ form, handleChange, errors }, ref) => {
    const [isBusinessInfoCollapsed, setIsBusinessInfoCollapsed] =
      useState(false);
    const [isBusinessAddressCollapsed, setIsBusinessAddressCollapsed] =
      useState(true);
    const [isBusinessDetailsCollapsed, setIsBusinessDetailsCollapsed] =
      useState(true);
    const [isAccountDetailsCollapsed, setIsAccountDetailsCollapsed] =
      useState(true);
    const [isBillingAddressCollapsed, setIsBillingAddressCollapsed] =
      useState(true);
    const [isCardDetailsCollapsed, setIsCardDetailsCollapsed] = useState(true);
    const [isMessengerCollapsed, setIsMessengerCollapsed] = useState(true);
    const [isSocialMediaCollapsed, setIsSocialMediaCollapsed] = useState(true);

    // Visibility states for business fields
    const [fieldVisibility, setFieldVisibility] = useState({
      // Business Information
      jobTitle: true,
      company: true,
      department: true,
      industry: true,
      workHours: true,
      companyWebsite: true,
      businessLinkedIn: true,
      businessTwitter: true,
      companyLogo: true,

      // Business Address
      officeBuilding: true,
      businessStreet: true,
      businessCity: true,
      businessState: true,
      businessZipCode: true,
      businessCountry: true,

      // Business Details
      workEmail: true,
      workPhone: true,
      workFax: true,
      resume: true,
      certifications: true,
      professionalNotes: true,

      // Account Details
      accountName: true,
      bankName: true,
      accountNumber: true,
      ifscCode: true,
      paypalEmail: true,

      // Billing Address
      billingApartment: true,
      billingStreet: true,
      billingCity: true,
      billingState: true,
      billingZipCode: true,
      billingCountry: true,

      // Card Details
      cardName: true,
      cardNumber: true,
      cardExpiry: true,
      cardCvv: true,

      // Company Messenger IDs
      iMessage: true,
      googleChat: true,
      discord: true,
      slack: true,
      wechat: true,
      kik: true,
      line: true,

      // Company Social Media
      facebook: true,
      instagram: true,
      twitter: true,
      linkedIn: true,
      snapchat: true,
      whatsapp: true,
      telegram: true,
      signal: true,
      skype: true,
      youtube: true,
      twitch: true,
      tiktok: true,
    });

    // Handle visibility toggle for a field
    const handleVisibilityToggle = (fieldName) => {
      setFieldVisibility((prev) => ({
        ...prev,
        [fieldName]: !prev[fieldName],
      }));
    };

    // Expose methods through the ref
    React.useImperativeHandle(ref, () => ({
      getFieldVisibility: () => fieldVisibility,
    }));

    const handleImagePicker = (field) => {
      Alert.alert(
        "Upload Image",
        "Choose a method",
        [
          { text: "Camera", onPress: () => openCamera(field) },
          { text: "Gallery", onPress: () => openGallery(field) },
          { text: "Cancel", style: "cancel" },
        ],
        { cancelable: true }
      );
    };

    const openCamera = async (field) => {
      try {
        const image = await ImagePicker.openCamera({
          cropping: true,
          compressImageQuality: 0.8,
        });
        handleChange(field, image.path);
      } catch (error) {
        console.log("Camera cancelled or failed:", error?.message);
      }
    };

    const openGallery = async (field) => {
      try {
        const image = await ImagePicker.openPicker({
          cropping: true,
          compressImageQuality: 0.8,
        });
        handleChange(field, image.path);
      } catch (error) {
        console.log("Gallery cancelled or failed:", error?.message);
      }
    };

    return (
      <View>
        {/* Business Information */}
        <CollapsibleHeader
          title="Business Information"
          isCollapsed={isBusinessInfoCollapsed}
          onToggle={() => setIsBusinessInfoCollapsed(!isBusinessInfoCollapsed)}
        />
        {!isBusinessInfoCollapsed && (
          <View style={{ zIndex: 3, position: "relative" }}>
            <InputField
              label="Job Title*"
              value={form.jobTitle}
              onChangeText={(val) => handleChange("jobTitle", val)}
              error={errors.jobTitle}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.jobTitle}
              onVisibilityToggle={() => handleVisibilityToggle("jobTitle")}
            />
            <InputField
              label="Company Name*"
              value={form.company}
              onChangeText={(val) => handleChange("company", val)}
              error={errors.company}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.company}
              onVisibilityToggle={() => handleVisibilityToggle("company")}
            />
            <InputField
              label="Department"
              value={form.department}
              onChangeText={(val) => handleChange("department", val)}
              error={errors.department}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.department}
              onVisibilityToggle={() => handleVisibilityToggle("department")}
            />
            <SelectField
              label="Industry"
              data={industryOptions}
              defaultValue={form.industry}
              onSelect={(item) => handleChange("industry", item.value)}
              backgroundColor="#f2f2f2"
              height={48}
              width="100%"
              showIcon={true}
              error={errors.industry}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.industry}
              onVisibilityToggle={() => handleVisibilityToggle("industry")}
            />
            <InputField
              label="Work Hours"
              value={form.workHours}
              onChangeText={(val) => handleChange("workHours", val)}
              error={errors.workHours}
              placeholder="e.g., 9:00 AM - 5:00 PM, Monday-Friday"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.workHours}
              onVisibilityToggle={() => handleVisibilityToggle("workHours")}
            />
            <InputField
              label="Company Website"
              value={form.companyWebsite}
              onChangeText={(val) => handleChange("companyWebsite", val)}
              error={errors.companyWebsite}
              placeholder="https://company.com"
              keyboardType="url"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.companyWebsite}
              onVisibilityToggle={() =>
                handleVisibilityToggle("companyWebsite")
              }
            />
            <InputField
              label="LinkedIn Profile"
              value={form.businessLinkedIn}
              onChangeText={(val) => handleChange("businessLinkedIn", val)}
              error={errors.businessLinkedIn}
              placeholder="https://linkedin.com/in/username"
              keyboardType="url"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.businessLinkedIn}
              onVisibilityToggle={() =>
                handleVisibilityToggle("businessLinkedIn")
              }
            />
            <InputField
              label="X Profile"
              value={form.businessTwitter}
              onChangeText={(val) => handleChange("businessTwitter", val)}
              error={errors.businessTwitter}
              placeholder="https://twitter.com/username"
              keyboardType="url"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.businessTwitter}
              onVisibilityToggle={() =>
                handleVisibilityToggle("businessTwitter")
              }
            />

            {/* Company Logo */}
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <Text style={styles.label}>Company Logo</Text>
              <TouchableOpacity
                style={{ padding: 5 }}
                onPress={() => handleVisibilityToggle("companyLogo")}
              >
                <Image
                  source={
                    fieldVisibility.companyLogo
                      ? icons.openEyeProfileIcon
                      : icons.closedEyeProfileIcon
                  }
                  style={{ width: 20, height: 20 }}
                  resizeMode="contain"
                />
              </TouchableOpacity>
            </View>
            <TouchableOpacity
              style={{
                backgroundColor: colors.inputBg,
                borderRadius: 10,
                padding: 12,
                marginBottom: 16,
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
              }}
              onPress={() => handleImagePicker("companyLogo")}
            >
              <Text style={{ color: form.companyLogo ? colors.black : "#888" }}>
                {form.companyLogo ? "Image Selected" : "Upload Company Logo"}
              </Text>
              <Image
                source={icons.rightArrowIcon}
                style={commonStyles.extraSmallIcon}
                resizeMode="contain"
              />
            </TouchableOpacity>
          </View>
        )}

        {/* Business Address */}
        <CollapsibleHeader
          title="Business Address"
          isCollapsed={isBusinessAddressCollapsed}
          onToggle={() =>
            setIsBusinessAddressCollapsed(!isBusinessAddressCollapsed)
          }
        />
        {!isBusinessAddressCollapsed && (
          <View style={{ zIndex: 2, position: "relative" }}>
            <InputField
              label="Office No./Building"
              value={form.officeBuilding}
              onChangeText={(val) => handleChange("officeBuilding", val)}
              error={errors.officeBuilding}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.officeBuilding}
              onVisibilityToggle={() =>
                handleVisibilityToggle("officeBuilding")
              }
            />
            <InputField
              label="Street"
              value={form.businessStreet}
              onChangeText={(val) => handleChange("businessStreet", val)}
              error={errors.businessStreet}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.businessStreet}
              onVisibilityToggle={() =>
                handleVisibilityToggle("businessStreet")
              }
            />
            <InputField
              label="City"
              value={form.businessCity}
              onChangeText={(val) => handleChange("businessCity", val)}
              error={errors.businessCity}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.businessCity}
              onVisibilityToggle={() => handleVisibilityToggle("businessCity")}
            />
            <InputField
              label="State/Province"
              value={form.businessState}
              onChangeText={(val) => handleChange("businessState", val)}
              error={errors.businessState}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.businessState}
              onVisibilityToggle={() => handleVisibilityToggle("businessState")}
            />
            <InputField
              label="ZIP/Postal Code"
              value={form.businessZipCode}
              onChangeText={(val) => handleChange("businessZipCode", val)}
              error={errors.businessZipCode}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.businessZipCode}
              onVisibilityToggle={() =>
                handleVisibilityToggle("businessZipCode")
              }
            />
            <InputField
              label="Country"
              value={form.businessCountry}
              onChangeText={(val) => handleChange("businessCountry", val)}
              error={errors.businessCountry}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.businessCountry}
              onVisibilityToggle={() =>
                handleVisibilityToggle("businessCountry")
              }
            />
          </View>
        )}

        {/* Business Details */}
        <CollapsibleHeader
          title="Business Details"
          isCollapsed={isBusinessDetailsCollapsed}
          onToggle={() =>
            setIsBusinessDetailsCollapsed(!isBusinessDetailsCollapsed)
          }
        />
        {!isBusinessDetailsCollapsed && (
          <View style={{ zIndex: 1, position: "relative" }}>
            <InputField
              label="Work Email"
              value={form.workEmail}
              onChangeText={(val) => handleChange("workEmail", val)}
              error={errors.workEmail}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.workEmail}
              onVisibilityToggle={() => handleVisibilityToggle("workEmail")}
            />
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <Text style={styles.label}>Work Phone</Text>
            </View>
            <PhoneNumberField
              value={form.workPhone}
              onChangeRaw={(val) => handleChange("workPhone", val)}
              setCountryCode={() => {}}
              error={errors.workPhone}
              setError={() => {}}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.workPhone}
              onVisibilityToggle={() => handleVisibilityToggle("workPhone")}
            />
            <InputField
              label="Work Fax Number"
              value={form.workFax}
              onChangeText={(val) => handleChange("workFax", val)}
              error={errors.workFax}
              keyboardType="phone-pad"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.workFax}
              onVisibilityToggle={() => handleVisibilityToggle("workFax")}
            />

            {/* Resume */}
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <Text style={styles.label}>Resume</Text>
              <TouchableOpacity
                style={{ padding: 5 }}
                onPress={() => handleVisibilityToggle("resume")}
              >
                <Image
                  source={
                    fieldVisibility.resume
                      ? icons.openEyeProfileIcon
                      : icons.closedEyeProfileIcon
                  }
                  style={{ width: 20, height: 20 }}
                  resizeMode="contain"
                />
              </TouchableOpacity>
            </View>
            <TouchableOpacity
              style={{
                backgroundColor: colors.inputBg,
                borderRadius: 10,
                padding: 12,
                marginBottom: 16,
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
              }}
              onPress={() => handleImagePicker("resume")}
            >
              <Text style={{ color: form.resume ? colors.black : "#888" }}>
                {form.resume ? "Resume Selected" : "Upload Resume"}
              </Text>
              <Image
                source={icons.rightArrowIcon}
                style={commonStyles.extraSmallIcon}
                resizeMode="contain"
              />
            </TouchableOpacity>

            {/* Professional Certifications */}
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <Text style={styles.label}>Professional Certifications</Text>
              <TouchableOpacity
                style={{ padding: 5 }}
                onPress={() => handleVisibilityToggle("certifications")}
              >
                <Image
                  source={
                    fieldVisibility.certifications
                      ? icons.openEyeProfileIcon
                      : icons.closedEyeProfileIcon
                  }
                  style={{ width: 20, height: 20 }}
                  resizeMode="contain"
                />
              </TouchableOpacity>
            </View>
            <TouchableOpacity
              style={{
                backgroundColor: colors.inputBg,
                borderRadius: 10,
                padding: 12,
                marginBottom: 16,
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
              }}
              onPress={() => handleImagePicker("certifications")}
            >
              <Text
                style={{ color: form.certifications ? colors.black : "#888" }}
              >
                {form.certifications
                  ? "Certifications Selected"
                  : "Upload Certifications"}
              </Text>
              <Image
                source={icons.rightArrowIcon}
                style={commonStyles.extraSmallIcon}
                resizeMode="contain"
              />
            </TouchableOpacity>

            {/* Professional Notes */}
            <InputField
              label="Professional Notes not covered above"
              value={form.professionalNotes}
              onChangeText={(val) => handleChange("professionalNotes", val)}
              error={errors.professionalNotes}
              placeholder="Add any additional professional information here"
              multiline={true}
              numberOfLines={4}
              textAlignVertical="top"
              style={{ height: 100, textAlignVertical: "top", paddingTop: 10 }}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.professionalNotes}
              onVisibilityToggle={() =>
                handleVisibilityToggle("professionalNotes")
              }
            />
          </View>
        )}

        {/* Account Details */}
        <CollapsibleHeader
          title="Account Details"
          isCollapsed={isAccountDetailsCollapsed}
          onToggle={() =>
            setIsAccountDetailsCollapsed(!isAccountDetailsCollapsed)
          }
        />
        {!isAccountDetailsCollapsed && (
          <View style={{ zIndex: 2, position: "relative" }}>
            <InputField
              label="Account Name"
              value={form.accountName}
              onChangeText={(val) => handleChange("accountName", val)}
              error={errors.accountName}
            />
            <InputField
              label="Bank Name"
              value={form.bankName}
              onChangeText={(val) => handleChange("bankName", val)}
              error={errors.bankName}
            />
            <InputField
              label="Account Number"
              value={form.accountNumber}
              onChangeText={(val) => handleChange("accountNumber", val)}
              error={errors.accountNumber}
            />
            <InputField
              label="IFSC Code"
              value={form.ifscCode}
              onChangeText={(val) => handleChange("ifscCode", val)}
              error={errors.ifscCode}
            />
            <InputField
              label="PayPal Email"
              value={form.paypalEmail}
              onChangeText={(val) => handleChange("paypalEmail", val)}
              error={errors.paypalEmail}
            />
          </View>
        )}

        {/* Billing Address */}
        <CollapsibleHeader
          title="Billing Address"
          isCollapsed={isBillingAddressCollapsed}
          onToggle={() =>
            setIsBillingAddressCollapsed(!isBillingAddressCollapsed)
          }
        />
        {!isBillingAddressCollapsed && (
          <View style={{ zIndex: 2, position: "relative" }}>
            <InputField
              label="Apartment/Office Building"
              value={form.billingApartment}
              onChangeText={(val) => handleChange("billingApartment", val)}
              error={errors.billingApartment}
            />
            <InputField
              label="Street"
              value={form.billingStreet}
              onChangeText={(val) => handleChange("billingStreet", val)}
              error={errors.billingStreet}
            />
            <InputField
              label="City"
              value={form.billingCity}
              onChangeText={(val) => handleChange("billingCity", val)}
              error={errors.billingCity}
            />
            <InputField
              label="State"
              value={form.billingState}
              onChangeText={(val) => handleChange("billingState", val)}
              error={errors.billingState}
            />
            <InputField
              label="ZIP/Postal Code"
              value={form.billingZipCode}
              onChangeText={(val) => handleChange("billingZipCode", val)}
              error={errors.billingZipCode}
            />
            <InputField
              label="Country"
              value={form.billingCountry}
              onChangeText={(val) => handleChange("billingCountry", val)}
              error={errors.billingCountry}
            />
          </View>
        )}

        {/* Card Details */}
        <CollapsibleHeader
          title="Card Details"
          isCollapsed={isCardDetailsCollapsed}
          onToggle={() => setIsCardDetailsCollapsed(!isCardDetailsCollapsed)}
        />
        {!isCardDetailsCollapsed && (
          <View style={{ zIndex: 2, position: "relative" }}>
            <InputField
              label="Name on Card"
              value={form.cardName}
              onChangeText={(val) => handleChange("cardName", val)}
              error={errors.cardName}
            />
            <InputField
              label="Card Number"
              value={form.cardNumber}
              onChangeText={(val) => handleChange("cardNumber", val)}
              error={errors.cardNumber}
            />
            <InputField
              label="Expiry Date"
              value={form.cardExpiry}
              onChangeText={(val) => handleChange("cardExpiry", val)}
              error={errors.cardExpiry}
            />
            <InputField
              label="CVV"
              value={form.cardCvv}
              onChangeText={(val) => handleChange("cardCvv", val)}
              error={errors.cardCvv}
            />
          </View>
        )}

        {/* Company Messenger IDs */}
        <CollapsibleHeader
          title="Company Messenger IDs"
          isCollapsed={isMessengerCollapsed}
          onToggle={() => setIsMessengerCollapsed(!isMessengerCollapsed)}
        />
        {!isMessengerCollapsed && (
          <View style={{ zIndex: 2, position: "relative" }}>
            <InputField
              label="iMessage"
              value={form.iMessage}
              onChangeText={(val) => handleChange("iMessage", val)}
              error={errors.iMessage}
            />
            <InputField
              label="Google Chat"
              value={form.googleChat}
              onChangeText={(val) => handleChange("googleChat", val)}
              error={errors.googleChat}
            />
            <InputField
              label="Discord"
              value={form.discord}
              onChangeText={(val) => handleChange("discord", val)}
              error={errors.discord}
            />
            <InputField
              label="Slack"
              value={form.slack}
              onChangeText={(val) => handleChange("slack", val)}
              error={errors.slack}
            />
            <InputField
              label="WeChat"
              value={form.wechat}
              onChangeText={(val) => handleChange("wechat", val)}
              error={errors.wechat}
            />
            <InputField
              label="Kik"
              value={form.kik}
              onChangeText={(val) => handleChange("kik", val)}
              error={errors.kik}
            />
            <InputField
              label="Line"
              value={form.line}
              onChangeText={(val) => handleChange("line", val)}
              error={errors.line}
            />
          </View>
        )}

        {/* Company Social Media */}
        <CollapsibleHeader
          title="Company Social Media"
          isCollapsed={isSocialMediaCollapsed}
          onToggle={() => setIsSocialMediaCollapsed(!isSocialMediaCollapsed)}
        />
        {!isSocialMediaCollapsed && (
          <View style={{ zIndex: 2, position: "relative" }}>
            <InputField
              label="Facebook"
              value={form.facebook}
              onChangeText={(val) => handleChange("facebook", val)}
              error={errors.facebook}
            />
            <InputField
              label="Instagram"
              value={form.instagram}
              onChangeText={(val) => handleChange("instagram", val)}
              error={errors.instagram}
            />
            <InputField
              label="Twitter"
              value={form.twitter}
              onChangeText={(val) => handleChange("twitter", val)}
              error={errors.twitter}
            />
            <InputField
              label="LinkedIn"
              value={form.linkedIn}
              onChangeText={(val) => handleChange("linkedIn", val)}
              error={errors.linkedIn}
            />
            <InputField
              label="Snapchat"
              value={form.snapchat}
              onChangeText={(val) => handleChange("snapchat", val)}
              error={errors.snapchat}
            />
            <InputField
              label="WhatsApp"
              value={form.whatsapp}
              onChangeText={(val) => handleChange("whatsapp", val)}
              error={errors.whatsapp}
            />
            <InputField
              label="Telegram"
              value={form.telegram}
              onChangeText={(val) => handleChange("telegram", val)}
              error={errors.telegram}
            />
            <InputField
              label="Signal"
              value={form.signal}
              onChangeText={(val) => handleChange("signal", val)}
              error={errors.signal}
            />
            <InputField
              label="Skype"
              value={form.skype}
              onChangeText={(val) => handleChange("skype", val)}
              error={errors.skype}
            />
            <InputField
              label="YouTube"
              value={form.youtube}
              onChangeText={(val) => handleChange("youtube", val)}
              error={errors.youtube}
            />
            <InputField
              label="Twitch"
              value={form.twitch}
              onChangeText={(val) => handleChange("twitch", val)}
              error={errors.twitch}
            />
            <InputField
              label="TikTok"
              value={form.tiktok}
              onChangeText={(val) => handleChange("tiktok", val)}
              error={errors.tiktok}
            />
          </View>
        )}
      </View>
    );
  }
);

BusinessSection.propTypes = {
  form: PropTypes.shape({
    // Business Information
    jobTitle: PropTypes.string,
    company: PropTypes.string,
    department: PropTypes.string,
    industry: PropTypes.string,
    workHours: PropTypes.string,
    companyWebsite: PropTypes.string,
    businessLinkedIn: PropTypes.string,
    businessTwitter: PropTypes.string,
    companyLogo: PropTypes.string,

    // Business Address
    officeBuilding: PropTypes.string,
    businessStreet: PropTypes.string,
    businessCity: PropTypes.string,
    businessState: PropTypes.string,
    businessZipCode: PropTypes.string,
    businessCountry: PropTypes.string,

    // Business Details
    workEmail: PropTypes.string,
    workPhone: PropTypes.string,
    workFax: PropTypes.string,
    resume: PropTypes.string,
    certifications: PropTypes.string,
    professionalNotes: PropTypes.string,

    // Account Details
    accountName: PropTypes.string,
    bankName: PropTypes.string,
    accountNumber: PropTypes.string,
    ifscCode: PropTypes.string,
    paypalEmail: PropTypes.string,

    // Billing Address
    billingApartment: PropTypes.string,
    billingStreet: PropTypes.string,
    billingCity: PropTypes.string,
    billingState: PropTypes.string,
    billingZipCode: PropTypes.string,
    billingCountry: PropTypes.string,

    // Card Details
    cardName: PropTypes.string,
    cardNumber: PropTypes.string,
    cardExpiry: PropTypes.string,
    cardCvv: PropTypes.string,

    // Company Messenger IDs
    iMessage: PropTypes.string,
    googleChat: PropTypes.string,
    discord: PropTypes.string,
    slack: PropTypes.string,
    wechat: PropTypes.string,
    kik: PropTypes.string,
    line: PropTypes.string,

    // Company Social Media
    facebook: PropTypes.string,
    instagram: PropTypes.string,
    twitter: PropTypes.string,
    linkedIn: PropTypes.string,
    snapchat: PropTypes.string,
    whatsapp: PropTypes.string,
    telegram: PropTypes.string,
    signal: PropTypes.string,
    skype: PropTypes.string,
    youtube: PropTypes.string,
    twitch: PropTypes.string,
    tiktok: PropTypes.string,
  }).isRequired,
  handleChange: PropTypes.func.isRequired,
  errors: PropTypes.object,
};

export default BusinessSection;
